// Simple debug panel for API monitoring
const React = window.React;
const { useState, useEffect } = React;
import { DEBUG_ENABLED, toggleDebug } from '/config.js';
import { getApiLogs, getApiStats, clearApiLogs } from '../utils/apiDebugger.js';

export const DebugPanel = () => {
    const [isOpen, setIsOpen] = useState(false);
    const [logs, setLogs] = useState([]);
    const [stats, setStats] = useState({});

    // Update logs and stats every 2 seconds when panel is open
    useEffect(() => {
        if (!isOpen) return;
        
        const updateData = () => {
            setLogs(getApiLogs());
            setStats(getApiStats());
        };
        
        updateData();
        const interval = setInterval(updateData, 2000);
        return () => clearInterval(interval);
    }, [isOpen]);

    if (!DEBUG_ENABLED) {
        return null;
    }

    return React.createElement('div', {
        className: 'fixed bottom-4 right-4 z-50'
    },
        // Toggle button
        React.createElement('button', {
            onClick: () => setIsOpen(!isOpen),
            className: 'bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg shadow-lg text-sm font-medium mb-2 flex items-center gap-2',
            title: 'Toggle API Debug Panel'
        },
            React.createElement('span', null, '🔧'),
            React.createElement('span', null, 'API Debug'),
            React.createElement('span', {
                className: `transform transition-transform ${isOpen ? 'rotate-180' : ''}`
            }, '▼')
        ),

        // Debug panel
        isOpen && React.createElement('div', {
            className: 'bg-gray-900 border border-gray-700 rounded-lg shadow-xl w-96 max-h-96 overflow-hidden'
        },
            // Header
            React.createElement('div', {
                className: 'bg-gray-800 px-4 py-3 border-b border-gray-700 flex justify-between items-center'
            },
                React.createElement('h3', {
                    className: 'text-white font-semibold text-sm'
                }, 'API Debug Panel'),
                React.createElement('div', {
                    className: 'flex gap-2'
                },
                    React.createElement('button', {
                        onClick: () => {
                            clearApiLogs();
                            setLogs([]);
                            setStats({});
                        },
                        className: 'text-gray-400 hover:text-white text-xs px-2 py-1 rounded',
                        title: 'Clear logs'
                    }, '🧹'),
                    React.createElement('button', {
                        onClick: toggleDebug,
                        className: 'text-gray-400 hover:text-white text-xs px-2 py-1 rounded',
                        title: 'Toggle debug mode'
                    }, '⚙️')
                )
            ),

            // Stats section
            React.createElement('div', {
                className: 'px-4 py-3 border-b border-gray-700'
            },
                React.createElement('h4', {
                    className: 'text-gray-300 text-xs font-medium mb-2'
                }, 'Statistics'),
                React.createElement('div', {
                    className: 'grid grid-cols-2 gap-2 text-xs'
                },
                    React.createElement('div', {
                        className: 'text-gray-400'
                    }, `Total: ${stats.total || 0}`),
                    React.createElement('div', {
                        className: 'text-green-400'
                    }, `Success: ${stats.successful || 0}`),
                    React.createElement('div', {
                        className: 'text-red-400'
                    }, `Failed: ${stats.failed || 0}`),
                    React.createElement('div', {
                        className: 'text-blue-400'
                    }, `Avg: ${stats.avgDuration || '0ms'}`)
                )
            ),

            // Logs section
            React.createElement('div', {
                className: 'max-h-64 overflow-y-auto'
            },
                React.createElement('div', {
                    className: 'px-4 py-2 bg-gray-800 text-gray-300 text-xs font-medium sticky top-0'
                }, 'Recent API Calls'),
                
                logs.length === 0 ? 
                    React.createElement('div', {
                        className: 'px-4 py-8 text-center text-gray-500 text-xs'
                    }, 'No API calls yet') :
                    
                logs.slice(0, 10).map((log, index) => 
                    React.createElement('div', {
                        key: log.id,
                        className: 'px-4 py-2 border-b border-gray-800 hover:bg-gray-800'
                    },
                        React.createElement('div', {
                            className: 'flex justify-between items-start mb-1'
                        },
                            React.createElement('span', {
                                className: 'text-white text-xs font-medium'
                            }, getApiName(log.endpoint)),
                            React.createElement('span', {
                                className: `text-xs ${getStatusColor(log)}`
                            }, getStatusText(log))
                        ),
                        React.createElement('div', {
                            className: 'text-gray-400 text-xs'
                        }, 
                            React.createElement('span', null, log.duration || 'Pending'),
                            ' • ',
                            React.createElement('span', null, new Date(log.timestamp).toLocaleTimeString())
                        ),
                        log.error && React.createElement('div', {
                            className: 'text-red-400 text-xs mt-1 truncate'
                        }, log.error)
                    )
                )
            )
        )
    );
};

// Helper functions
const getApiName = (endpoint) => {
    if (!endpoint) return 'Unknown';
    if (endpoint.includes('/images/generations')) return 'Image Gen';
    if (endpoint.includes('/chat/completions')) return 'Chat';
    return 'API';
};

const getStatusColor = (log) => {
    if (log.error) return 'text-red-400';
    if (log.success) return 'text-green-400';
    if (log.status) return 'text-yellow-400';
    return 'text-gray-400';
};

const getStatusText = (log) => {
    if (log.error) return 'Error';
    if (log.success) return 'Success';
    if (log.status) return log.status;
    return 'Pending';
};
