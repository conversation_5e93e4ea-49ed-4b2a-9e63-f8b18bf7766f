# API Debug Guide

This guide explains how to use the API debugging functionality in the Thumbnail Spark application.

## APIs Used

The application makes calls to the following APIs:

### 1. OpenAI Image Generation API
- **Endpoint**: `https://api.openai.com/v1/images/generations`
- **Purpose**: Generate thumbnail images
- **Model**: `gpt-image-1`
- **Location**: `src/App.jsx` (handleGenerateClick function)

### 2. OpenAI Chat Completions API
- **Endpoint**: `https://api.openai.com/v1/chat/completions`
- **Purpose**: Enhance user prompts for better titles
- **Model**: `gpt-3.5-turbo-1106`
- **Location**: `src/utils/openaiPromptEnhancer.js`

### 3. Unsplash Images (Static)
- **Endpoint**: `https://images.unsplash.com/`
- **Purpose**: Background images for templates
- **Usage**: Static URLs for template backgrounds

## How to Enable Debug Mode

### Method 1: URL Parameter
Add `?debug=true` to the URL:
```
http://localhost:3000?debug=true
```

### Method 2: Browser Console
Open browser console and run:
```javascript
localStorage.setItem('api-debug', 'true');
window.location.reload();
```

### Method 3: Debug Panel Toggle
When debug mode is active, click the ⚙️ button in the debug panel.

## Debug Features

### 1. Console Logging
When debug mode is enabled, you'll see detailed logs in the browser console:

- **🚀 API Request logs** - Show endpoint, method, payload, and timestamp
- **✅ API Response logs** - Show status, duration, response data, and token usage
- **❌ Error logs** - Show detailed error information
- **📊 Statistics** - Automatic stats every 10 API calls

### 2. Debug Panel
A floating debug panel appears in the bottom-right corner showing:

- **Real-time statistics**: Total calls, success/failure rates, average duration
- **Recent API calls**: Last 10 API calls with status and timing
- **Error details**: Error messages for failed calls
- **Controls**: Clear logs and toggle debug mode

### 3. Browser Console Tools
When debug mode is active, these tools are available in the console:

```javascript
// View all API logs
window.apiDebug.logs()

// View statistics
window.apiDebug.stats()

// Clear all logs
window.apiDebug.clear()

// Print statistics
window.apiDebug.print()
```

## What Gets Logged

### Request Information
- Timestamp
- API endpoint
- HTTP method
- Request payload (API key is hidden)
- Request ID for tracking

### Response Information
- Response status and status text
- Response duration
- Response data (including token usage for chat completions)
- Error details (if any)
- Success/failure status

### Statistics
- Total API calls made
- Number of successful calls
- Number of failed calls
- Success rate percentage
- Average response duration

## Example Debug Output

```
🚀 API Request #1 - Image Generation
📍 Endpoint: https://api.openai.com/v1/images/generations
⚡ Method: POST
📦 Payload: {model: "gpt-image-1", prompt: "...", size: "1536x1024"}
⏰ Time: 2024-01-20T15:30:00.000Z

✅ API Response #1 - 2340ms
📊 Status: 200 OK
⏱️ Duration: 2340ms
📄 Response Data: {data: [{b64_json: "..."}]}
🖼️ Generated Images: 1
```

## Troubleshooting

### Debug Mode Not Working
1. Check browser console for errors
2. Ensure localStorage is enabled
3. Try refreshing the page after enabling debug mode

### No Logs Appearing
1. Verify debug mode is enabled: `localStorage.getItem('api-debug')`
2. Make sure you're making API calls (generate an image or improve a prompt)
3. Check if console is filtering out logs

### Debug Panel Not Showing
1. Ensure debug mode is enabled
2. Check if the panel is hidden behind other elements
3. Try scrolling to the bottom-right of the page

## Disabling Debug Mode

### Method 1: Debug Panel
Click the ⚙️ button in the debug panel.

### Method 2: Browser Console
```javascript
localStorage.setItem('api-debug', 'false');
window.location.reload();
```

### Method 3: Clear Storage
```javascript
localStorage.removeItem('api-debug');
window.location.reload();
```

## Security Note

The debug functionality automatically hides sensitive information like API keys in the logs. However, be cautious when sharing debug logs as they may contain prompts and other application data.
