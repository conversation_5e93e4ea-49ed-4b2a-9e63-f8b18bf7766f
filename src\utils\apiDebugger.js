// Simple API debugger utility for tracking OpenAI API calls
import { DEBUG_ENABLED } from '/config.js';

// Store API call logs
let apiLogs = [];
const MAX_LOGS = 50;

// API call counter
let callCounter = 0;

/**
 * Log API request details
 */
export const logApiRequest = (endpoint, method, payload, headers = {}) => {
    if (!DEBUG_ENABLED) return;
    
    callCounter++;
    const timestamp = new Date().toISOString();
    const logEntry = {
        id: callCounter,
        timestamp,
        type: 'REQUEST',
        endpoint,
        method,
        payload: payload ? JSON.parse(JSON.stringify(payload)) : null,
        headers: { ...headers, Authorization: '[HIDDEN]' }, // Hide API key
        startTime: Date.now()
    };
    
    // Add to logs array
    apiLogs.unshift(logEntry);
    if (apiLogs.length > MAX_LOGS) {
        apiLogs = apiLogs.slice(0, MAX_LOGS);
    }
    
    // Console output
    console.group(`🚀 API Request #${callCounter} - ${getApiName(endpoint)}`);
    console.log('📍 Endpoint:', endpoint);
    console.log('⚡ Method:', method);
    console.log('📦 Payload:', payload);
    console.log('⏰ Time:', timestamp);
    console.groupEnd();
    
    return callCounter;
};

/**
 * Log API response details
 */
export const logApiResponse = (requestId, response, data, error = null) => {
    if (!DEBUG_ENABLED) return;
    
    const timestamp = new Date().toISOString();
    const duration = Date.now() - (apiLogs.find(log => log.id === requestId)?.startTime || Date.now());
    
    const logEntry = {
        id: requestId,
        timestamp,
        type: 'RESPONSE',
        status: response?.status,
        statusText: response?.statusText,
        duration: `${duration}ms`,
        data: data ? JSON.parse(JSON.stringify(data)) : null,
        error: error ? error.message : null,
        success: !error && response?.ok
    };
    
    // Update the request log with response info
    const requestLog = apiLogs.find(log => log.id === requestId);
    if (requestLog) {
        Object.assign(requestLog, logEntry);
    }
    
    // Console output
    const emoji = error ? '❌' : response?.ok ? '✅' : '⚠️';
    console.group(`${emoji} API Response #${requestId} - ${duration}ms`);
    console.log('📊 Status:', response?.status, response?.statusText);
    console.log('⏱️ Duration:', duration + 'ms');
    
    if (error) {
        console.error('💥 Error:', error);
    } else if (data) {
        console.log('📄 Response Data:', data);
        
        // Log specific OpenAI response details
        if (data.usage) {
            console.log('🎯 Token Usage:', data.usage);
        }
        if (data.data && Array.isArray(data.data)) {
            console.log('🖼️ Generated Images:', data.data.length);
        }
    }
    
    console.groupEnd();
};

/**
 * Get friendly API name from endpoint
 */
const getApiName = (endpoint) => {
    if (endpoint.includes('/images/generations')) return 'Image Generation';
    if (endpoint.includes('/chat/completions')) return 'Chat Completion';
    if (endpoint.includes('unsplash.com')) return 'Unsplash Images';
    return 'Unknown API';
};

/**
 * Get all API logs
 */
export const getApiLogs = () => {
    return [...apiLogs];
};

/**
 * Clear all API logs
 */
export const clearApiLogs = () => {
    apiLogs = [];
    callCounter = 0;
    console.log('🧹 API logs cleared');
};

/**
 * Get API statistics
 */
export const getApiStats = () => {
    const total = apiLogs.length;
    const successful = apiLogs.filter(log => log.success).length;
    const failed = apiLogs.filter(log => log.error).length;
    const avgDuration = apiLogs.reduce((sum, log) => {
        const duration = parseInt(log.duration) || 0;
        return sum + duration;
    }, 0) / total || 0;
    
    return {
        total,
        successful,
        failed,
        successRate: total > 0 ? ((successful / total) * 100).toFixed(1) + '%' : '0%',
        avgDuration: Math.round(avgDuration) + 'ms'
    };
};

/**
 * Print API statistics to console
 */
export const printApiStats = () => {
    if (!DEBUG_ENABLED) return;
    
    const stats = getApiStats();
    console.group('📊 API Statistics');
    console.log('📈 Total Calls:', stats.total);
    console.log('✅ Successful:', stats.successful);
    console.log('❌ Failed:', stats.failed);
    console.log('📊 Success Rate:', stats.successRate);
    console.log('⏱️ Avg Duration:', stats.avgDuration);
    console.groupEnd();
};

// Auto-print stats every 10 API calls
let lastStatsCall = 0;
export const maybeShowStats = () => {
    if (DEBUG_ENABLED && callCounter > 0 && callCounter % 10 === 0 && callCounter !== lastStatsCall) {
        lastStatsCall = callCounter;
        printApiStats();
    }
};

// Expose debug functions to window for easy access in browser console
if (DEBUG_ENABLED && typeof window !== 'undefined') {
    window.apiDebug = {
        logs: getApiLogs,
        stats: getApiStats,
        clear: clearApiLogs,
        print: printApiStats
    };
    console.log('🔧 API Debug tools available: window.apiDebug');
}
